<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Generator Widget Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .demo-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .demo-section h2 {
            color: #555;
            margin-top: 0;
        }

        .widget-embed {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }

        .responsive-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .responsive-frame {
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
        }

        .responsive-frame h3 {
            background: #f8f9fa;
            margin: 0;
            padding: 10px;
            font-size: 14px;
            color: #666;
        }

        .responsive-frame iframe {
            width: 100%;
            height: 300px;
            border: none;
        }

        @media (max-width: 768px) {
            .responsive-demo {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🚀 Quote Generator Widget Demo</h1>
        
        <div class="demo-section">
            <h2>📱 Interactive Widget</h2>
            <p>This is the embeddable Quote Generator widget with fullscreen functionality. Click the expand button in the top-right corner to go fullscreen!</p>
            
            <iframe 
                src="./iframe.html" 
                class="widget-embed"
                title="Quote Generator Widget">
            </iframe>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="controlWidget('toggleFullscreen')">Toggle Fullscreen</button>
                <button class="btn btn-secondary" onclick="controlWidget('reload')">Reload Widget</button>
                <button class="btn btn-secondary" onclick="showEmbedCode()">Show Embed Code</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>✨ Features</h2>
            <ul class="feature-list">
                <li>One-click fullscreen toggle with smooth animations</li>
                <li>Responsive design that works on all devices</li>
                <li>Keyboard shortcuts (ESC to exit, F11 to toggle)</li>
                <li>Native fullscreen API support with fallbacks</li>
                <li>Loading indicator with spinner animation</li>
                <li>Cross-origin message handling for communication</li>
                <li>External API for programmatic control</li>
                <li>Glass morphism design with backdrop blur</li>
                <li>Hover effects and visual feedback</li>
                <li>Mobile-optimized controls and layout</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🔧 How to Embed</h2>
            <p>Simply include this iframe in your website:</p>
            
            <div class="code-block" id="embed-code">
&lt;iframe 
    src="https://your-domain.com/iframe.html" 
    width="800" 
    height="600"
    frameborder="0"
    allowfullscreen
    title="Quote Generator Widget"&gt;
&lt;/iframe&gt;
            </div>

            <h3>📋 Programmatic Control</h3>
            <p>You can control the widget programmatically:</p>
            
            <div class="code-block">
// Access the widget API
const widget = document.querySelector('iframe').contentWindow.QuoteGeneratorWidget;

// Toggle fullscreen
widget.toggleFullscreen();

// Enter fullscreen
widget.enterFullscreen();

// Exit fullscreen  
widget.exitFullscreen();

// Check if fullscreen
console.log(widget.isFullscreen());

// Reload the widget
widget.reload();
            </div>
        </div>

        <div class="demo-section">
            <h2>📱 Responsive Preview</h2>
            <div class="responsive-demo">
                <div class="responsive-frame">
                    <h3>Desktop View</h3>
                    <iframe src="./iframe.html" title="Desktop Preview"></iframe>
                </div>
                <div class="responsive-frame">
                    <h3>Mobile View</h3>
                    <iframe src="./iframe.html" title="Mobile Preview" style="transform: scale(0.7); transform-origin: top left; width: 142.86%; height: 428px;"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Control the main widget
        function controlWidget(action) {
            const iframe = document.querySelector('.widget-embed');
            const widget = iframe.contentWindow.QuoteGeneratorWidget;
            
            if (widget && widget[action]) {
                widget[action]();
            } else {
                console.log('Widget API not available or action not found:', action);
            }
        }

        // Show embed code
        function showEmbedCode() {
            const code = document.getElementById('embed-code');
            code.scrollIntoView({ behavior: 'smooth' });
            
            // Highlight the code block
            code.style.background = '#fff3cd';
            code.style.border = '2px solid #ffc107';
            
            setTimeout(() => {
                code.style.background = '#f8f9fa';
                code.style.border = '1px solid #e9ecef';
            }, 2000);
        }

        // Listen for messages from the widget
        window.addEventListener('message', function(event) {
            console.log('Message from widget:', event.data);
        });
    </script>
</body>
</html>
