<div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; overflow: hidden; border-radius: 20px; z-index: 800;">
    <iframe
        id="quote-generator-iframe"
        src="https://quote-gen.bloodandtreasure.com"
        title="Quote Generator"
        frameborder="0"
        scrolling="auto"
        allowfullscreen
        data-loading="true"
        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; display: block; border-radius: 20px;">
    </iframe>
    <div id="loading-indicator" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-family: Arial, sans-serif; color: #666; z-index: 1;">Loading Quote Generator...</div>
</div>

<script>
    // Remove loading indicator when iframe loads
    const iframe = document.getElementById('quote-generator-iframe');
    const loading = document.getElementById('loading-indicator');

    iframe.addEventListener('load', function() {
        iframe.removeAttribute('data-loading');
        loading.style.display = 'none';
    });

    // Handle iframe communication if needed
    window.addEventListener('message', function(event) {
        // Verify origin for security
        if (event.origin !== 'https://quote-gen.bloodandtreasure.com') {
            return;
        }

        // Handle any messages from the embedded app
        console.log('Message from embedded app:', event.data);
    });

    // Responsive handling
    function adjustIframe() {
        // Ensure iframe fills container completely
        iframe.style.width = '100%';
        iframe.style.height = '100%';
    }

    // Adjust on load and resize
    window.addEventListener('load', adjustIframe);
    window.addEventListener('resize', adjustIframe);
</script>
