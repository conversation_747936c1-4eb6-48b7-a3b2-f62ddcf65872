<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Generator - Embeddable Widget</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        @media (max-width: 768px) {
            .launch-button { padding: 16px 32px !important; font-size: 16px !important; }
            .modal-overlay { padding: 10px !important; }
            .modal-content { width: 98vw !important; height: 95vh !important; border-radius: 15px !important; }
            .close-btn { width: 35px !important; height: 35px !important; top: 10px !important; right: 10px !important; }
            .close-btn svg { width: 18px !important; height: 18px !important; }
        }
    </style>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center; padding: 20px; margin: 0;"
    <!-- Launch Button -->
    <button id="launch-btn" class="launch-button" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 20px 40px; font-size: 18px; font-weight: 600; border-radius: 50px; cursor: pointer; box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3); transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 12px 40px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(102, 126, 234, 0.3)'" onmousedown="this.style.transform='translateY(0)'" onmouseup="this.style.transform='translateY(-2px)'">
        Generate your quote
    </button>

    <!-- Modal Overlay -->
    <div id="modal-overlay" class="modal-overlay" style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(10px); z-index: 9999; display: none; align-items: center; justify-content: center; padding: 20px;">
        <div class="modal-content" style="width: 95vw; height: 90vh; max-width: 1200px; max-height: 800px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); border-radius: 20px; border: 1px solid rgba(255, 255, 255, 0.2); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); position: relative; overflow: hidden;">
            <iframe
                id="quote-generator-iframe"
                src="https://quote-gen.bloodandtreasure.com"
                title="Quote Generator"
                frameborder="0"
                scrolling="auto"
                allowfullscreen
                style="width: 100%; height: 100%; border: none; border-radius: inherit;">
            </iframe>

            <div id="loading-indicator" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: rgba(255, 255, 255, 0.8); font-size: 16px; z-index: 5; display: flex; align-items: center; gap: 10px;">
                <div class="loading-spinner" style="width: 20px; height: 20px; border: 2px solid rgba(255, 255, 255, 0.3); border-top: 2px solid rgba(255, 255, 255, 0.8); border-radius: 50%; animation: spin 1s linear infinite;"></div>
                Loading Quote Generator...
            </div>

            <button id="close-btn" class="close-btn" title="Close" style="position: absolute; top: 15px; right: 15px; width: 40px; height: 40px; background: rgba(255, 255, 255, 0.9); border: none; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); z-index: 10;" onmouseover="this.style.background='rgba(255, 255, 255, 1)'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.9)'; this.style.transform='scale(1)'" onmousedown="this.style.transform='scale(0.95)'" onmouseup="this.style.transform='scale(1.1)'">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 20px; height: 20px; color: #333;">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
    </div>

    <script>
        // Get DOM elements
        const launchBtn = document.getElementById('launch-btn');
        const modalOverlay = document.getElementById('modal-overlay');
        const closeBtn = document.getElementById('close-btn');
        const iframe = document.getElementById('quote-generator-iframe');
        const loading = document.getElementById('loading-indicator');

        // State management
        let isModalOpen = false;
        let iframeLoaded = false;

        // Open modal
        function openModal() {
            modalOverlay.style.display = 'flex';
            isModalOpen = true;
            document.body.style.overflow = 'hidden';

            // Load iframe if not already loaded
            if (!iframeLoaded) {
                iframe.src = 'https://quote-gen.bloodandtreasure.com';
            }
        }

        // Close modal
        function closeModal() {
            modalOverlay.style.display = 'none';
            isModalOpen = false;
            document.body.style.overflow = '';
        }

        // Remove loading indicator when iframe loads
        iframe.addEventListener('load', function() {
            iframeLoaded = true;
            loading.style.display = 'none';
        });

        // Event listeners
        launchBtn.addEventListener('click', openModal);
        closeBtn.addEventListener('click', closeModal);

        // Click outside modal content to close
        modalOverlay.addEventListener('click', function(e) {
            if (e.target === modalOverlay) {
                closeModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to close modal
            if (e.key === 'Escape' && isModalOpen) {
                closeModal();
            }
        });

        // Handle iframe communication
        window.addEventListener('message', function(event) {
            // Verify origin for security
            if (event.origin !== 'https://quote-gen.bloodandtreasure.com') {
                return;
            }

            // Handle messages from the embedded app
            console.log('Message from embedded app:', event.data);

            // Handle specific message types
            if (event.data && typeof event.data === 'object') {
                switch (event.data.type) {
                    case 'close':
                        closeModal();
                        break;
                    case 'resize':
                        // Handle resize requests from embedded app
                        break;
                }
            }
        });

        // Expose API for external control
        window.QuoteGeneratorWidget = {
            open: openModal,
            close: closeModal,
            isOpen: () => isModalOpen,
            reload: () => {
                if (iframeLoaded) {
                    loading.style.display = 'flex';
                    iframe.src = iframe.src;
                    iframeLoaded = false;
                }
            }
        };
    </script>
</body>
</html>
<script>
    // Adjust on load and resize
    window.addEventListener('load', adjustIframe);
    window.addEventListener('resize', adjustIframe);
</script>
</script>
