<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Generator - Embeddable Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .widget-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            height: 600px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .widget-container.fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            max-width: none;
            border-radius: 0;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
        }

        .widget-container.fullscreen .iframe-wrapper {
            width: min(90vw, 1200px);
            height: min(85vh, 800px);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .iframe-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }

        #quote-generator-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            border-radius: inherit;
        }

        .controls {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 10;
            display: flex;
            gap: 10px;
        }

        .widget-container.fullscreen .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10001;
        }

        .control-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        .control-btn svg {
            width: 20px;
            height: 20px;
            color: #333;
        }

        .close-btn {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10002;
        }

        .widget-container.fullscreen .close-btn {
            display: flex;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        .close-btn:active {
            transform: scale(0.95);
        }

        .close-btn svg {
            width: 24px;
            height: 24px;
            color: #333;
        }

        #loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            z-index: 5;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .widget-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .widget-container:hover .widget-info {
            opacity: 1;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .widget-container {
                height: 500px;
                margin: 10px;
            }

            .controls {
                top: 10px;
                right: 10px;
            }

            .control-btn {
                width: 35px;
                height: 35px;
            }

            .control-btn svg {
                width: 18px;
                height: 18px;
            }

            .widget-container.fullscreen {
                padding: 20px;
            }

            .widget-container.fullscreen .iframe-wrapper {
                width: 95vw;
                height: 90vh;
            }

            .close-btn {
                width: 45px;
                height: 45px;
                top: 15px;
                right: 15px;
            }

            .close-btn svg {
                width: 22px;
                height: 22px;
            }
        }
    </style>
</head>
<body>
    <div id="widget-container" class="widget-container">
        <div class="iframe-wrapper">
            <iframe
                id="quote-generator-iframe"
                src="https://quote-gen.bloodandtreasure.com"
                title="Quote Generator"
                frameborder="0"
                scrolling="auto"
                allowfullscreen
                data-loading="true">
            </iframe>

            <div id="loading-indicator">
                <div class="loading-spinner"></div>
                Loading Quote Generator...
            </div>
        </div>

        <div class="controls">
            <button id="fullscreen-btn" class="control-btn" title="Toggle Fullscreen">
                <svg id="expand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                </svg>
                <svg id="compress-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
                    <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"/>
                </svg>
            </button>
        </div>

        <button id="close-btn" class="close-btn" title="Close Fullscreen">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
        </button>

        <div class="widget-info">
            Quote Generator Widget • Click expand for fullscreen
        </div>
    </div>

    <script>
        // Get DOM elements
        const iframe = document.getElementById('quote-generator-iframe');
        const loading = document.getElementById('loading-indicator');
        const widgetContainer = document.getElementById('widget-container');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const closeBtn = document.getElementById('close-btn');
        const expandIcon = document.getElementById('expand-icon');
        const compressIcon = document.getElementById('compress-icon');

        // State management
        let isFullscreen = false;
        let originalParent = null;
        let originalNextSibling = null;

        // Remove loading indicator when iframe loads
        iframe.addEventListener('load', function() {
            iframe.removeAttribute('data-loading');
            loading.style.display = 'none';
        });

        // Fullscreen functionality
        function toggleFullscreen() {
            if (!isFullscreen) {
                enterFullscreen();
            } else {
                exitFullscreen();
            }
        }

        function enterFullscreen() {
            // Store original position
            originalParent = widgetContainer.parentNode;
            originalNextSibling = widgetContainer.nextSibling;

            // Add fullscreen class
            widgetContainer.classList.add('fullscreen');

            // Move to body for proper fullscreen
            document.body.appendChild(widgetContainer);

            // Update state and icons
            isFullscreen = true;
            expandIcon.style.display = 'none';
            compressIcon.style.display = 'block';

            // Try to use native fullscreen API if available
            if (widgetContainer.requestFullscreen) {
                widgetContainer.requestFullscreen().catch(err => {
                    console.log('Fullscreen API not supported or denied:', err);
                });
            } else if (widgetContainer.webkitRequestFullscreen) {
                widgetContainer.webkitRequestFullscreen();
            } else if (widgetContainer.msRequestFullscreen) {
                widgetContainer.msRequestFullscreen();
            }

            // Prevent body scroll when in fullscreen
            document.body.style.overflow = 'hidden';
        }

        function exitFullscreen() {
            // Remove fullscreen class
            widgetContainer.classList.remove('fullscreen');

            // Restore original position
            if (originalNextSibling) {
                originalParent.insertBefore(widgetContainer, originalNextSibling);
            } else {
                originalParent.appendChild(widgetContainer);
            }

            // Update state and icons
            isFullscreen = false;
            expandIcon.style.display = 'block';
            compressIcon.style.display = 'none';

            // Exit native fullscreen if active
            if (document.exitFullscreen) {
                document.exitFullscreen().catch(err => {
                    console.log('Exit fullscreen error:', err);
                });
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }

            // Restore body scroll
            document.body.style.overflow = '';
        }

        // Event listeners
        fullscreenBtn.addEventListener('click', toggleFullscreen);
        closeBtn.addEventListener('click', exitFullscreen);

        // Click outside to close fullscreen
        widgetContainer.addEventListener('click', function(e) {
            if (isFullscreen && e.target === widgetContainer) {
                exitFullscreen();
            }
        });

        // Handle native fullscreen change events
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);

        function handleFullscreenChange() {
            const isNativeFullscreen = !!(
                document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.msFullscreenElement
            );

            // If native fullscreen was exited but our state says we're fullscreen
            if (!isNativeFullscreen && isFullscreen) {
                exitFullscreen();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to exit fullscreen
            if (e.key === 'Escape' && isFullscreen) {
                exitFullscreen();
            }

            // F11 or F to toggle fullscreen
            if ((e.key === 'F11' || (e.key === 'f' && e.ctrlKey)) && !e.defaultPrevented) {
                e.preventDefault();
                toggleFullscreen();
            }
        });

        // Handle iframe communication
        window.addEventListener('message', function(event) {
            // Verify origin for security
            if (event.origin !== 'https://quote-gen.bloodandtreasure.com') {
                return;
            }

            // Handle messages from the embedded app
            console.log('Message from embedded app:', event.data);

            // Handle specific message types
            if (event.data && typeof event.data === 'object') {
                switch (event.data.type) {
                    case 'requestFullscreen':
                        if (!isFullscreen) {
                            enterFullscreen();
                        }
                        break;
                    case 'exitFullscreen':
                        if (isFullscreen) {
                            exitFullscreen();
                        }
                        break;
                    case 'resize':
                        // Handle resize requests from embedded app
                        adjustIframe();
                        break;
                }
            }
        });

        // Responsive handling
        function adjustIframe() {
            // Ensure iframe fills container completely
            iframe.style.width = '100%';
            iframe.style.height = '100%';
        }

        // Handle window resize
        window.addEventListener('resize', adjustIframe);

        // Initialize
        adjustIframe();

        // Expose API for external control
        window.QuoteGeneratorWidget = {
            toggleFullscreen: toggleFullscreen,
            enterFullscreen: enterFullscreen,
            exitFullscreen: exitFullscreen,
            isFullscreen: () => isFullscreen,
            reload: () => {
                loading.style.display = 'flex';
                iframe.src = iframe.src;
            }
        };
    </script>
</body>
</html>

    // Adjust on load and resize
    window.addEventListener('load', adjustIframe);
    window.addEventListener('resize', adjustIframe);
</script>
